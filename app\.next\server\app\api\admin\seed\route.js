/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/seed/route";
exports.ids = ["app/api/admin/seed/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_admin_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/seed/route.ts */ \"(rsc)/./src/app/api/admin/seed/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/seed/route\",\n        pathname: \"/api/admin/seed\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/seed/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\api\\\\admin\\\\seed\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_admin_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/seed/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/admin/seed/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/Product */ \"(rsc)/./src/lib/models/Product.ts\");\n/* harmony import */ var _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CustomerDebt */ \"(rsc)/./src/lib/models/CustomerDebt.ts\");\n\n\n\n\n// Sample Products Data - Realistic Filipino Sari-Sari Store Inventory\nconst sampleProducts = [\n    {\n        name: 'Lucky Me! Pancit Canton Original',\n        image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400',\n        netWeight: '60g',\n        price: 15.00,\n        stockQuantity: 50,\n        category: 'instant foods'\n    },\n    {\n        name: 'Coca-Cola 330ml',\n        image: 'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=400',\n        netWeight: '330ml',\n        price: 25.00,\n        stockQuantity: 30,\n        category: 'beverages'\n    },\n    {\n        name: 'Argentina Corned Beef 150g',\n        image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',\n        netWeight: '150g',\n        price: 45.00,\n        stockQuantity: 20,\n        category: 'canned goods'\n    },\n    {\n        name: 'Chippy Barbecue 110g',\n        image: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400',\n        netWeight: '110g',\n        price: 35.00,\n        stockQuantity: 25,\n        category: 'snacks'\n    },\n    {\n        name: 'Safeguard Soap Classic White',\n        image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',\n        netWeight: '90g',\n        price: 18.00,\n        stockQuantity: 15,\n        category: 'personal care'\n    },\n    {\n        name: 'Tide Powder Detergent 35g',\n        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',\n        netWeight: '35g',\n        price: 12.00,\n        stockQuantity: 40,\n        category: 'household'\n    },\n    {\n        name: 'Silver Swan Soy Sauce 200ml',\n        image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400',\n        netWeight: '200ml',\n        price: 22.00,\n        stockQuantity: 18,\n        category: 'condiments'\n    },\n    {\n        name: 'Bear Brand Milk Drink 300ml',\n        image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400',\n        netWeight: '300ml',\n        price: 28.00,\n        stockQuantity: 12,\n        category: 'dairy'\n    },\n    {\n        name: 'Skyflakes Crackers 250g',\n        image: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400',\n        netWeight: '250g',\n        price: 32.00,\n        stockQuantity: 22,\n        category: 'snacks'\n    },\n    {\n        name: 'Century Tuna Flakes in Oil 180g',\n        image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',\n        netWeight: '180g',\n        price: 38.00,\n        stockQuantity: 16,\n        category: 'canned goods'\n    }\n];\n// Sample Customer Debts Data - Realistic Utang Scenarios\nconst sampleDebts = [\n    {\n        customerName: 'Maria Santos',\n        productName: 'Lucky Me! Pancit Canton Original',\n        productPrice: 15.00,\n        quantity: 3,\n        dateOfDebt: new Date('2024-01-15'),\n        isPaid: false,\n        notes: 'Regular customer, good payment history'\n    },\n    {\n        customerName: 'Juan Dela Cruz',\n        productName: 'Coca-Cola 330ml',\n        productPrice: 25.00,\n        quantity: 2,\n        dateOfDebt: new Date('2024-01-10'),\n        isPaid: true,\n        paidDate: new Date('2024-01-12'),\n        notes: 'Paid on time'\n    },\n    {\n        customerName: 'Ana Reyes',\n        productName: 'Argentina Corned Beef 150g',\n        productPrice: 45.00,\n        quantity: 1,\n        dateOfDebt: new Date('2024-01-18'),\n        isPaid: false,\n        notes: 'Will pay on Friday'\n    },\n    {\n        customerName: 'Pedro Garcia',\n        productName: 'Chippy Barbecue 110g',\n        productPrice: 35.00,\n        quantity: 2,\n        dateOfDebt: new Date('2024-01-12'),\n        isPaid: true,\n        paidDate: new Date('2024-01-14'),\n        notes: 'Paid with exact amount'\n    },\n    {\n        customerName: 'Maria Santos',\n        productName: 'Bear Brand Milk Drink 300ml',\n        productPrice: 28.00,\n        quantity: 1,\n        dateOfDebt: new Date('2024-01-20'),\n        isPaid: false,\n        notes: 'For her child'\n    },\n    {\n        customerName: 'Rosa Mendoza',\n        productName: 'Safeguard Soap Classic White',\n        productPrice: 18.00,\n        quantity: 2,\n        dateOfDebt: new Date('2024-01-16'),\n        isPaid: false,\n        notes: 'New customer'\n    },\n    {\n        customerName: 'Carlos Villanueva',\n        productName: 'Century Tuna Flakes in Oil 180g',\n        productPrice: 38.00,\n        quantity: 1,\n        dateOfDebt: new Date('2024-01-08'),\n        isPaid: true,\n        paidDate: new Date('2024-01-11'),\n        notes: 'Loyal customer'\n    },\n    {\n        customerName: 'Luz Fernandez',\n        productName: 'Tide Powder Detergent 35g',\n        productPrice: 12.00,\n        quantity: 3,\n        dateOfDebt: new Date('2024-01-19'),\n        isPaid: false,\n        notes: 'Bulk purchase'\n    },\n    {\n        customerName: 'Ana Reyes',\n        productName: 'Silver Swan Soy Sauce 200ml',\n        productPrice: 22.00,\n        quantity: 1,\n        dateOfDebt: new Date('2024-01-21'),\n        isPaid: false,\n        notes: 'For cooking'\n    },\n    {\n        customerName: 'Juan Dela Cruz',\n        productName: 'Skyflakes Crackers 250g',\n        productPrice: 32.00,\n        quantity: 1,\n        dateOfDebt: new Date('2024-01-17'),\n        isPaid: true,\n        paidDate: new Date('2024-01-19'),\n        notes: 'Quick payment'\n    }\n];\n// POST /api/admin/seed - Seed the database with sample data\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { clearExisting = false } = await request.json().catch(()=>({}));\n        let result = {\n            success: true,\n            message: 'Database seeded successfully',\n            data: {\n                products: {\n                    created: 0,\n                    existing: 0\n                },\n                debts: {\n                    created: 0,\n                    existing: 0\n                }\n            }\n        };\n        // Clear existing data if requested\n        if (clearExisting) {\n            await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].deleteMany({});\n            await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].deleteMany({});\n            result.message += ' (existing data cleared)';\n        }\n        // Seed Products\n        const existingProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({});\n        if (existingProducts.length === 0) {\n            const createdProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].insertMany(sampleProducts);\n            result.data.products.created = createdProducts.length;\n        } else {\n            result.data.products.existing = existingProducts.length;\n        }\n        // Seed Customer Debts\n        const existingDebts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({});\n        if (existingDebts.length === 0) {\n            const createdDebts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].insertMany(sampleDebts);\n            result.data.debts.created = createdDebts.length;\n        } else {\n            result.data.debts.existing = existingDebts.length;\n        }\n        // Calculate statistics\n        const allProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({});\n        const allDebts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({});\n        const totalStockValue = allProducts.reduce((sum, product)=>sum + product.price * product.stockQuantity, 0);\n        const totalDebtAmount = allDebts.reduce((sum, debt)=>sum + debt.totalAmount, 0);\n        const unpaidDebtAmount = allDebts.filter((debt)=>!debt.isPaid).reduce((sum, debt)=>sum + debt.totalAmount, 0);\n        result.data = {\n            ...result.data,\n            statistics: {\n                totalProducts: allProducts.length,\n                totalDebts: allDebts.length,\n                totalStockValue: parseFloat(totalStockValue.toFixed(2)),\n                totalDebtAmount: parseFloat(totalDebtAmount.toFixed(2)),\n                unpaidDebtAmount: parseFloat(unpaidDebtAmount.toFixed(2))\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Error seeding database:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to seed database',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/admin/seed - Get seeding status\nasync function GET() {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const productCount = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments();\n        const debtCount = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                isSeeded: productCount > 0 || debtCount > 0,\n                productCount,\n                debtCount,\n                sampleDataAvailable: {\n                    products: sampleProducts.length,\n                    debts: sampleDebts.length\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error checking seed status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to check seed status'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/seed/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/CustomerDebt.ts":
/*!****************************************!*\
  !*** ./src/lib/models/CustomerDebt.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CustomerDebtSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    customerName: {\n        type: String,\n        required: [\n            true,\n            'Customer name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Customer name cannot exceed 100 characters'\n        ]\n    },\n    productName: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    productPrice: {\n        type: Number,\n        required: [\n            true,\n            'Product price is required'\n        ],\n        min: [\n            0,\n            'Product price cannot be negative'\n        ]\n    },\n    quantity: {\n        type: Number,\n        required: [\n            true,\n            'Quantity is required'\n        ],\n        min: [\n            1,\n            'Quantity must be at least 1'\n        ],\n        validate: {\n            validator: function(value) {\n                return Number.isInteger(value) && value > 0;\n            },\n            message: 'Quantity must be a positive integer'\n        }\n    },\n    totalAmount: {\n        type: Number,\n        required: [\n            true,\n            'Total amount is required'\n        ],\n        min: [\n            0,\n            'Total amount cannot be negative'\n        ]\n    },\n    dateOfDebt: {\n        type: Date,\n        required: [\n            true,\n            'Date of debt is required'\n        ],\n        default: Date.now\n    },\n    isPaid: {\n        type: Boolean,\n        default: false\n    },\n    paidDate: {\n        type: Date,\n        default: null\n    },\n    notes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Notes cannot exceed 500 characters'\n        ],\n        default: ''\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nCustomerDebtSchema.index({\n    customerName: 1\n});\nCustomerDebtSchema.index({\n    isPaid: 1\n});\nCustomerDebtSchema.index({\n    dateOfDebt: -1\n});\nCustomerDebtSchema.index({\n    customerName: 1,\n    isPaid: 1\n});\n// Virtual for days since debt was created\nCustomerDebtSchema.virtual('daysSinceDebt').get(function() {\n    const now = new Date();\n    const debtDate = new Date(this.dateOfDebt);\n    const diffTime = Math.abs(now.getTime() - debtDate.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware to calculate total amount and handle paid date\nCustomerDebtSchema.pre('save', function(next) {\n    // Calculate total amount\n    this.totalAmount = this.productPrice * this.quantity;\n    // Set paid date when marking as paid\n    if (this.isPaid && !this.paidDate) {\n        this.paidDate = new Date();\n    }\n    // Clear paid date when marking as unpaid\n    if (!this.isPaid && this.paidDate) {\n        this.paidDate = undefined;\n    }\n    next();\n});\n// Static method to get debt summary by customer\nCustomerDebtSchema.statics.getDebtSummaryByCustomer = async function(customerName) {\n    const debts = await this.find({\n        customerName\n    }).sort({\n        dateOfDebt: -1\n    });\n    const totalDebt = debts.reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    const totalUnpaid = debts.filter((debt)=>!debt.isPaid).reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    return {\n        customerName,\n        totalDebt,\n        totalUnpaid,\n        debtCount: debts.length,\n        unpaidCount: debts.filter((debt)=>!debt.isPaid).length,\n        debts\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CustomerDebt || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('CustomerDebt', CustomerDebtSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/CustomerDebt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/Product.ts":
/*!***********************************!*\
  !*** ./src/lib/models/Product.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    image: {\n        type: String,\n        trim: true,\n        default: ''\n    },\n    netWeight: {\n        type: String,\n        required: [\n            true,\n            'Net weight is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Net weight cannot exceed 50 characters'\n        ]\n    },\n    price: {\n        type: Number,\n        required: [\n            true,\n            'Price is required'\n        ],\n        min: [\n            0,\n            'Price cannot be negative'\n        ],\n        validate: {\n            validator: function(value) {\n                return value >= 0;\n            },\n            message: 'Price must be a positive number'\n        }\n    },\n    stockQuantity: {\n        type: Number,\n        required: [\n            true,\n            'Stock quantity is required'\n        ],\n        min: [\n            0,\n            'Stock quantity cannot be negative'\n        ],\n        default: 0\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: {\n            values: [\n                'snacks',\n                'canned goods',\n                'beverages',\n                'personal care',\n                'household',\n                'condiments',\n                'instant foods',\n                'dairy',\n                'frozen',\n                'others'\n            ],\n            message: 'Invalid category'\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nProductSchema.index({\n    name: 1\n});\nProductSchema.index({\n    category: 1\n});\nProductSchema.index({\n    stockQuantity: 1\n});\n// Virtual for low stock indicator\nProductSchema.virtual('isLowStock').get(function() {\n    return this.stockQuantity <= 5;\n});\n// Pre-save middleware to ensure data consistency\nProductSchema.pre('save', function(next) {\n    if (this.price < 0) {\n        this.price = 0;\n    }\n    if (this.stockQuantity < 0) {\n        this.stockQuantity = 0;\n    }\n    next();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Product', ProductSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/Product.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sari-sari-store';\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        // Check if connection is still alive\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1) {\n            return cached.conn;\n        } else {\n            // Reset cached connection if it's not alive\n            cached.conn = null;\n            cached.promise = null;\n        }\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log('✅ Connected to MongoDB');\n            return mongoose.connection;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', e);\n        throw new Error('Failed to connect to database');\n    }\n    return cached.conn;\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n});\n// Handle process termination\nprocess.on('SIGINT', async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n    console.log('🛑 MongoDB connection closed due to app termination');\n    process.exit(0);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();