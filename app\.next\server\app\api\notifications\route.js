/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/route";
exports.ids = ["app/api/notifications/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/notifications/route.ts */ \"(rsc)/./src/app/api/notifications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/route\",\n        pathname: \"/api/notifications\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\api\\\\notifications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/notifications/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/notifications/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/Product */ \"(rsc)/./src/lib/models/Product.ts\");\n/* harmony import */ var _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CustomerDebt */ \"(rsc)/./src/lib/models/CustomerDebt.ts\");\n\n\n\n\n// GET /api/notifications - Get system notifications and alerts\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const notifications = [];\n        // 1. Low Stock Alerts\n        const lowStockProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            stockQuantity: {\n                $lte: 5,\n                $gt: 0\n            }\n        }).lean();\n        lowStockProducts.forEach((product)=>{\n            notifications.push({\n                id: `low_stock_${product._id}`,\n                type: 'low_stock',\n                title: 'Low Stock Alert',\n                message: `${product.name} is running low (${product.stockQuantity} left)`,\n                severity: product.stockQuantity <= 2 ? 'high' : 'medium',\n                data: {\n                    productId: product._id,\n                    productName: product.name,\n                    currentStock: product.stockQuantity,\n                    category: product.category,\n                    price: product.price\n                },\n                createdAt: new Date()\n            });\n        });\n        // 2. Out of Stock Alerts\n        const outOfStockProducts = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            stockQuantity: 0\n        }).lean();\n        outOfStockProducts.forEach((product)=>{\n            notifications.push({\n                id: `out_of_stock_${product._id}`,\n                type: 'out_of_stock',\n                title: 'Out of Stock',\n                message: `${product.name} is out of stock`,\n                severity: 'critical',\n                data: {\n                    productId: product._id,\n                    productName: product.name,\n                    category: product.category,\n                    price: product.price\n                },\n                createdAt: new Date()\n            });\n        });\n        // 3. Overdue Debt Alerts\n        const overdueThreshold = new Date();\n        overdueThreshold.setDate(overdueThreshold.getDate() - 7); // 7 days overdue\n        const overdueDebts = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({\n            isPaid: false,\n            dateOfDebt: {\n                $lt: overdueThreshold\n            }\n        }).lean();\n        // Group overdue debts by customer\n        const overdueByCustomer = overdueDebts.reduce((acc, debt)=>{\n            if (!acc[debt.customerName]) {\n                acc[debt.customerName] = {\n                    debts: [],\n                    totalAmount: 0,\n                    oldestDebt: debt.dateOfDebt\n                };\n            }\n            acc[debt.customerName].debts.push(debt);\n            acc[debt.customerName].totalAmount += debt.totalAmount;\n            if (debt.dateOfDebt < acc[debt.customerName].oldestDebt) {\n                acc[debt.customerName].oldestDebt = debt.dateOfDebt;\n            }\n            return acc;\n        }, {});\n        Object.entries(overdueByCustomer).forEach(([customerName, data])=>{\n            const daysOverdue = Math.floor((Date.now() - new Date(data.oldestDebt).getTime()) / (1000 * 60 * 60 * 24));\n            notifications.push({\n                id: `overdue_debt_${customerName.replace(/\\s+/g, '_')}`,\n                type: 'overdue_debt',\n                title: 'Overdue Debt',\n                message: `${customerName} has ₱${data.totalAmount.toFixed(2)} in overdue debts (${daysOverdue} days)`,\n                severity: daysOverdue > 14 ? 'critical' : daysOverdue > 7 ? 'high' : 'medium',\n                data: {\n                    customerName,\n                    totalAmount: data.totalAmount,\n                    debtCount: data.debts.length,\n                    daysOverdue,\n                    oldestDebt: data.oldestDebt,\n                    debts: data.debts\n                },\n                createdAt: new Date()\n            });\n        });\n        // 4. High Risk Customer Alerts\n        const customerRiskAnalysis = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: '$customerName',\n                    totalDebts: {\n                        $sum: 1\n                    },\n                    totalAmount: {\n                        $sum: '$totalAmount'\n                    },\n                    unpaidAmount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        false\n                                    ]\n                                },\n                                '$totalAmount',\n                                0\n                            ]\n                        }\n                    },\n                    unpaidCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        false\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    paymentRate: {\n                        $avg: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        '$isPaid',\n                                        true\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    }\n                }\n            },\n            {\n                $match: {\n                    $or: [\n                        {\n                            unpaidAmount: {\n                                $gt: 200\n                            }\n                        },\n                        {\n                            paymentRate: {\n                                $lt: 0.5\n                            }\n                        },\n                        {\n                            unpaidCount: {\n                                $gt: 3\n                            }\n                        }\n                    ]\n                }\n            }\n        ]);\n        customerRiskAnalysis.forEach((customer)=>{\n            const riskFactors = [];\n            let severity = 'medium';\n            if (customer.unpaidAmount > 500) {\n                riskFactors.push('Very high unpaid amount');\n                severity = 'critical';\n            } else if (customer.unpaidAmount > 200) {\n                riskFactors.push('High unpaid amount');\n                severity = 'high';\n            }\n            if (customer.paymentRate < 0.3) {\n                riskFactors.push('Very low payment rate');\n                severity = 'critical';\n            } else if (customer.paymentRate < 0.5) {\n                riskFactors.push('Low payment rate');\n                if (severity !== 'critical') severity = 'high';\n            }\n            if (customer.unpaidCount > 5) {\n                riskFactors.push('Many unpaid debts');\n                severity = 'critical';\n            } else if (customer.unpaidCount > 3) {\n                riskFactors.push('Multiple unpaid debts');\n                if (severity !== 'critical') severity = 'high';\n            }\n            notifications.push({\n                id: `high_risk_${customer._id.replace(/\\s+/g, '_')}`,\n                type: 'high_risk_customer',\n                title: 'High Risk Customer',\n                message: `${customer._id} is a high-risk customer (₱${customer.unpaidAmount.toFixed(2)} unpaid)`,\n                severity,\n                data: {\n                    customerName: customer._id,\n                    unpaidAmount: customer.unpaidAmount,\n                    unpaidCount: customer.unpaidCount,\n                    paymentRate: Math.round(customer.paymentRate * 100),\n                    riskFactors\n                },\n                createdAt: new Date()\n            });\n        });\n        // 5. Payment Reminders (debts 3-6 days old, not yet overdue)\n        const reminderStartDate = new Date();\n        reminderStartDate.setDate(reminderStartDate.getDate() - 6);\n        const reminderEndDate = new Date();\n        reminderEndDate.setDate(reminderEndDate.getDate() - 3);\n        const paymentReminders = await _lib_models_CustomerDebt__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({\n            isPaid: false,\n            dateOfDebt: {\n                $gte: reminderStartDate,\n                $lte: reminderEndDate\n            }\n        }).lean();\n        const remindersByCustomer = paymentReminders.reduce((acc, debt)=>{\n            if (!acc[debt.customerName]) {\n                acc[debt.customerName] = {\n                    debts: [],\n                    totalAmount: 0\n                };\n            }\n            acc[debt.customerName].debts.push(debt);\n            acc[debt.customerName].totalAmount += debt.totalAmount;\n            return acc;\n        }, {});\n        Object.entries(remindersByCustomer).forEach(([customerName, data])=>{\n            notifications.push({\n                id: `payment_reminder_${customerName.replace(/\\s+/g, '_')}`,\n                type: 'payment_reminder',\n                title: 'Payment Reminder',\n                message: `Remind ${customerName} about ₱${data.totalAmount.toFixed(2)} in pending payments`,\n                severity: 'low',\n                data: {\n                    customerName,\n                    totalAmount: data.totalAmount,\n                    debtCount: data.debts.length,\n                    debts: data.debts\n                },\n                createdAt: new Date()\n            });\n        });\n        // Sort notifications by severity and creation date\n        const severityOrder = {\n            critical: 4,\n            high: 3,\n            medium: 2,\n            low: 1\n        };\n        notifications.sort((a, b)=>{\n            if (severityOrder[a.severity] !== severityOrder[b.severity]) {\n                return severityOrder[b.severity] - severityOrder[a.severity];\n            }\n            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n        });\n        // Summary statistics\n        const summary = {\n            total: notifications.length,\n            critical: notifications.filter((n)=>n.severity === 'critical').length,\n            high: notifications.filter((n)=>n.severity === 'high').length,\n            medium: notifications.filter((n)=>n.severity === 'medium').length,\n            low: notifications.filter((n)=>n.severity === 'low').length,\n            byType: {\n                low_stock: notifications.filter((n)=>n.type === 'low_stock').length,\n                out_of_stock: notifications.filter((n)=>n.type === 'out_of_stock').length,\n                overdue_debt: notifications.filter((n)=>n.type === 'overdue_debt').length,\n                high_risk_customer: notifications.filter((n)=>n.type === 'high_risk_customer').length,\n                payment_reminder: notifications.filter((n)=>n.type === 'payment_reminder').length\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                notifications,\n                summary,\n                generatedAt: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching notifications:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch notifications',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/notifications/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/CustomerDebt.ts":
/*!****************************************!*\
  !*** ./src/lib/models/CustomerDebt.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CustomerDebtSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    customerName: {\n        type: String,\n        required: [\n            true,\n            'Customer name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Customer name cannot exceed 100 characters'\n        ]\n    },\n    productName: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    productPrice: {\n        type: Number,\n        required: [\n            true,\n            'Product price is required'\n        ],\n        min: [\n            0,\n            'Product price cannot be negative'\n        ]\n    },\n    quantity: {\n        type: Number,\n        required: [\n            true,\n            'Quantity is required'\n        ],\n        min: [\n            1,\n            'Quantity must be at least 1'\n        ],\n        validate: {\n            validator: function(value) {\n                return Number.isInteger(value) && value > 0;\n            },\n            message: 'Quantity must be a positive integer'\n        }\n    },\n    totalAmount: {\n        type: Number,\n        required: [\n            true,\n            'Total amount is required'\n        ],\n        min: [\n            0,\n            'Total amount cannot be negative'\n        ]\n    },\n    dateOfDebt: {\n        type: Date,\n        required: [\n            true,\n            'Date of debt is required'\n        ],\n        default: Date.now\n    },\n    isPaid: {\n        type: Boolean,\n        default: false\n    },\n    paidDate: {\n        type: Date,\n        default: null\n    },\n    notes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Notes cannot exceed 500 characters'\n        ],\n        default: ''\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nCustomerDebtSchema.index({\n    customerName: 1\n});\nCustomerDebtSchema.index({\n    isPaid: 1\n});\nCustomerDebtSchema.index({\n    dateOfDebt: -1\n});\nCustomerDebtSchema.index({\n    customerName: 1,\n    isPaid: 1\n});\n// Virtual for days since debt was created\nCustomerDebtSchema.virtual('daysSinceDebt').get(function() {\n    const now = new Date();\n    const debtDate = new Date(this.dateOfDebt);\n    const diffTime = Math.abs(now.getTime() - debtDate.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n});\n// Pre-save middleware to calculate total amount and handle paid date\nCustomerDebtSchema.pre('save', function(next) {\n    // Calculate total amount\n    this.totalAmount = this.productPrice * this.quantity;\n    // Set paid date when marking as paid\n    if (this.isPaid && !this.paidDate) {\n        this.paidDate = new Date();\n    }\n    // Clear paid date when marking as unpaid\n    if (!this.isPaid && this.paidDate) {\n        this.paidDate = undefined;\n    }\n    next();\n});\n// Static method to get debt summary by customer\nCustomerDebtSchema.statics.getDebtSummaryByCustomer = async function(customerName) {\n    const debts = await this.find({\n        customerName\n    }).sort({\n        dateOfDebt: -1\n    });\n    const totalDebt = debts.reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    const totalUnpaid = debts.filter((debt)=>!debt.isPaid).reduce((sum, debt)=>sum + debt.totalAmount, 0);\n    return {\n        customerName,\n        totalDebt,\n        totalUnpaid,\n        debtCount: debts.length,\n        unpaidCount: debts.filter((debt)=>!debt.isPaid).length,\n        debts\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CustomerDebt || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('CustomerDebt', CustomerDebtSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/CustomerDebt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/Product.ts":
/*!***********************************!*\
  !*** ./src/lib/models/Product.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    image: {\n        type: String,\n        trim: true,\n        default: ''\n    },\n    netWeight: {\n        type: String,\n        required: [\n            true,\n            'Net weight is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Net weight cannot exceed 50 characters'\n        ]\n    },\n    price: {\n        type: Number,\n        required: [\n            true,\n            'Price is required'\n        ],\n        min: [\n            0,\n            'Price cannot be negative'\n        ],\n        validate: {\n            validator: function(value) {\n                return value >= 0;\n            },\n            message: 'Price must be a positive number'\n        }\n    },\n    stockQuantity: {\n        type: Number,\n        required: [\n            true,\n            'Stock quantity is required'\n        ],\n        min: [\n            0,\n            'Stock quantity cannot be negative'\n        ],\n        default: 0\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: {\n            values: [\n                'snacks',\n                'canned goods',\n                'beverages',\n                'personal care',\n                'household',\n                'condiments',\n                'instant foods',\n                'dairy',\n                'frozen',\n                'others'\n            ],\n            message: 'Invalid category'\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nProductSchema.index({\n    name: 1\n});\nProductSchema.index({\n    category: 1\n});\nProductSchema.index({\n    stockQuantity: 1\n});\n// Virtual for low stock indicator\nProductSchema.virtual('isLowStock').get(function() {\n    return this.stockQuantity <= 5;\n});\n// Pre-save middleware to ensure data consistency\nProductSchema.pre('save', function(next) {\n    if (this.price < 0) {\n        this.price = 0;\n    }\n    if (this.stockQuantity < 0) {\n        this.stockQuantity = 0;\n    }\n    next();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Product', ProductSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/Product.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sari-sari-store';\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        // Check if connection is still alive\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1) {\n            return cached.conn;\n        } else {\n            // Reset cached connection if it's not alive\n            cached.conn = null;\n            cached.promise = null;\n        }\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log('✅ Connected to MongoDB');\n            return mongoose.connection;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', e);\n        throw new Error('Failed to connect to database');\n    }\n    return cached.conn;\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n});\n// Handle process termination\nprocess.on('SIGINT', async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n    console.log('🛑 MongoDB connection closed due to app termination');\n    process.exit(0);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vbmdvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBU2hDLE1BQU1DLGNBQ0pDLFFBQVFDLEdBQUcsQ0FBQ0YsV0FBVyxJQUFJO0FBRTdCLElBQUksQ0FBQ0EsYUFBYTtJQUNoQixNQUFNLElBQUlHLE1BQ1I7QUFFSjtBQUVBOzs7O0NBSUMsR0FDRCxJQUFJQyxTQUFTQyxPQUFPTixRQUFRO0FBRTVCLElBQUksQ0FBQ0ssUUFBUTtJQUNYQSxTQUFTQyxPQUFPTixRQUFRLEdBQUc7UUFBRU8sTUFBTTtRQUFNQyxTQUFTO0lBQUs7QUFDekQ7QUFFQSxlQUFlQztJQUNiLElBQUlKLE9BQU9FLElBQUksRUFBRTtRQUNmLHFDQUFxQztRQUNyQyxJQUFJUCw0REFBbUIsQ0FBQ1csVUFBVSxLQUFLLEdBQUc7WUFDeEMsT0FBT04sT0FBT0UsSUFBSTtRQUNwQixPQUFPO1lBQ0wsNENBQTRDO1lBQzVDRixPQUFPRSxJQUFJLEdBQUc7WUFDZEYsT0FBT0csT0FBTyxHQUFHO1FBQ25CO0lBQ0Y7SUFFQSxJQUFJLENBQUNILE9BQU9HLE9BQU8sRUFBRTtRQUNuQixNQUFNSSxPQUFPO1lBQ1hDLGdCQUFnQjtZQUNoQkMsYUFBYTtZQUNiQywwQkFBMEI7WUFDMUJDLGlCQUFpQjtZQUNqQkMsUUFBUTtRQUNWO1FBRUFaLE9BQU9HLE9BQU8sR0FBR1IsdURBQWdCLENBQUNDLGFBQWFXLE1BQU1PLElBQUksQ0FBQyxDQUFDbkI7WUFDekRvQixRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPckIsU0FBU1UsVUFBVTtRQUM1QjtJQUNGO0lBRUEsSUFBSTtRQUNGTCxPQUFPRSxJQUFJLEdBQUcsTUFBTUYsT0FBT0csT0FBTztJQUNwQyxFQUFFLE9BQU9jLEdBQUc7UUFDVmpCLE9BQU9HLE9BQU8sR0FBRztRQUNqQlksUUFBUUcsS0FBSyxDQUFDLCtCQUErQkQ7UUFDN0MsTUFBTSxJQUFJbEIsTUFBTTtJQUNsQjtJQUVBLE9BQU9DLE9BQU9FLElBQUk7QUFDcEI7QUFFQSw0QkFBNEI7QUFDNUJQLDBEQUFtQixDQUFDd0IsRUFBRSxDQUFDLGFBQWE7SUFDbENKLFFBQVFDLEdBQUcsQ0FBQztBQUNkO0FBRUFyQiwwREFBbUIsQ0FBQ3dCLEVBQUUsQ0FBQyxTQUFTLENBQUNDO0lBQy9CTCxRQUFRRyxLQUFLLENBQUMsZ0NBQWdDRTtBQUNoRDtBQUVBekIsMERBQW1CLENBQUN3QixFQUFFLENBQUMsZ0JBQWdCO0lBQ3JDSixRQUFRQyxHQUFHLENBQUM7QUFDZDtBQUVBLDZCQUE2QjtBQUM3Qm5CLFFBQVFzQixFQUFFLENBQUMsVUFBVTtJQUNuQixNQUFNeEIsMERBQW1CLENBQUMwQixLQUFLO0lBQy9CTixRQUFRQyxHQUFHLENBQUM7SUFDWm5CLFFBQVF5QixJQUFJLENBQUM7QUFDZjtBQUVBLGlFQUFlbEIsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxERUxMXFxPbmVEcml2ZVxcRGVza3RvcFxcdGluZGFoYW5cXGFwcFxcc3JjXFxsaWJcXG1vbmdvZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gJ21vbmdvb3NlJztcblxuZGVjbGFyZSBnbG9iYWwge1xuICB2YXIgbW9uZ29vc2U6IHtcbiAgICBjb25uOiBhbnkgfCBudWxsO1xuICAgIHByb21pc2U6IFByb21pc2U8YW55PiB8IG51bGw7XG4gIH07XG59XG5cbmNvbnN0IE1PTkdPREJfVVJJID1cbiAgcHJvY2Vzcy5lbnYuTU9OR09EQl9VUkkgfHwgJ21vbmdvZGI6Ly9sb2NhbGhvc3Q6MjcwMTcvc2FyaS1zYXJpLXN0b3JlJztcblxuaWYgKCFNT05HT0RCX1VSSSkge1xuICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgJ1BsZWFzZSBkZWZpbmUgdGhlIE1PTkdPREJfVVJJIGVudmlyb25tZW50IHZhcmlhYmxlIGluc2lkZSAuZW52LmxvY2FsJ1xuICApO1xufVxuXG4vKipcbiAqIEdsb2JhbCBpcyB1c2VkIGhlcmUgdG8gbWFpbnRhaW4gYSBjYWNoZWQgY29ubmVjdGlvbiBhY3Jvc3MgaG90IHJlbG9hZHNcbiAqIGluIGRldmVsb3BtZW50LiBUaGlzIHByZXZlbnRzIGNvbm5lY3Rpb25zIGdyb3dpbmcgZXhwb25lbnRpYWxseVxuICogZHVyaW5nIEFQSSBSb3V0ZSB1c2FnZS5cbiAqL1xubGV0IGNhY2hlZCA9IGdsb2JhbC5tb25nb29zZTtcblxuaWYgKCFjYWNoZWQpIHtcbiAgY2FjaGVkID0gZ2xvYmFsLm1vbmdvb3NlID0geyBjb25uOiBudWxsLCBwcm9taXNlOiBudWxsIH07XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGNvbm5lY3REQigpIHtcbiAgaWYgKGNhY2hlZC5jb25uKSB7XG4gICAgLy8gQ2hlY2sgaWYgY29ubmVjdGlvbiBpcyBzdGlsbCBhbGl2ZVxuICAgIGlmIChtb25nb29zZS5jb25uZWN0aW9uLnJlYWR5U3RhdGUgPT09IDEpIHtcbiAgICAgIHJldHVybiBjYWNoZWQuY29ubjtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gUmVzZXQgY2FjaGVkIGNvbm5lY3Rpb24gaWYgaXQncyBub3QgYWxpdmVcbiAgICAgIGNhY2hlZC5jb25uID0gbnVsbDtcbiAgICAgIGNhY2hlZC5wcm9taXNlID0gbnVsbDtcbiAgICB9XG4gIH1cblxuICBpZiAoIWNhY2hlZC5wcm9taXNlKSB7XG4gICAgY29uc3Qgb3B0cyA9IHtcbiAgICAgIGJ1ZmZlckNvbW1hbmRzOiBmYWxzZSxcbiAgICAgIG1heFBvb2xTaXplOiAxMCxcbiAgICAgIHNlcnZlclNlbGVjdGlvblRpbWVvdXRNUzogNTAwMCxcbiAgICAgIHNvY2tldFRpbWVvdXRNUzogNDUwMDAsXG4gICAgICBmYW1pbHk6IDRcbiAgICB9O1xuXG4gICAgY2FjaGVkLnByb21pc2UgPSBtb25nb29zZS5jb25uZWN0KE1PTkdPREJfVVJJLCBvcHRzKS50aGVuKChtb25nb29zZSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBDb25uZWN0ZWQgdG8gTW9uZ29EQicpO1xuICAgICAgcmV0dXJuIG1vbmdvb3NlLmNvbm5lY3Rpb247XG4gICAgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIGNhY2hlZC5jb25uID0gYXdhaXQgY2FjaGVkLnByb21pc2U7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBjYWNoZWQucHJvbWlzZSA9IG51bGw7XG4gICAgY29uc29sZS5lcnJvcign4p2MIE1vbmdvREIgY29ubmVjdGlvbiBlcnJvcjonLCBlKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjb25uZWN0IHRvIGRhdGFiYXNlJyk7XG4gIH1cblxuICByZXR1cm4gY2FjaGVkLmNvbm47XG59XG5cbi8vIENvbm5lY3Rpb24gZXZlbnQgaGFuZGxlcnNcbm1vbmdvb3NlLmNvbm5lY3Rpb24ub24oJ2Nvbm5lY3RlZCcsICgpID0+IHtcbiAgY29uc29sZS5sb2coJ/CflJcgTW9uZ29vc2UgY29ubmVjdGVkIHRvIE1vbmdvREInKTtcbn0pO1xuXG5tb25nb29zZS5jb25uZWN0aW9uLm9uKCdlcnJvcicsIChlcnIpID0+IHtcbiAgY29uc29sZS5lcnJvcign4p2MIE1vbmdvb3NlIGNvbm5lY3Rpb24gZXJyb3I6JywgZXJyKTtcbn0pO1xuXG5tb25nb29zZS5jb25uZWN0aW9uLm9uKCdkaXNjb25uZWN0ZWQnLCAoKSA9PiB7XG4gIGNvbnNvbGUubG9nKCfwn5SMIE1vbmdvb3NlIGRpc2Nvbm5lY3RlZCBmcm9tIE1vbmdvREInKTtcbn0pO1xuXG4vLyBIYW5kbGUgcHJvY2VzcyB0ZXJtaW5hdGlvblxucHJvY2Vzcy5vbignU0lHSU5UJywgYXN5bmMgKCkgPT4ge1xuICBhd2FpdCBtb25nb29zZS5jb25uZWN0aW9uLmNsb3NlKCk7XG4gIGNvbnNvbGUubG9nKCfwn5uRIE1vbmdvREIgY29ubmVjdGlvbiBjbG9zZWQgZHVlIHRvIGFwcCB0ZXJtaW5hdGlvbicpO1xuICBwcm9jZXNzLmV4aXQoMCk7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgY29ubmVjdERCO1xuIl0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiTU9OR09EQl9VUkkiLCJwcm9jZXNzIiwiZW52IiwiRXJyb3IiLCJjYWNoZWQiLCJnbG9iYWwiLCJjb25uIiwicHJvbWlzZSIsImNvbm5lY3REQiIsImNvbm5lY3Rpb24iLCJyZWFkeVN0YXRlIiwib3B0cyIsImJ1ZmZlckNvbW1hbmRzIiwibWF4UG9vbFNpemUiLCJzZXJ2ZXJTZWxlY3Rpb25UaW1lb3V0TVMiLCJzb2NrZXRUaW1lb3V0TVMiLCJmYW1pbHkiLCJjb25uZWN0IiwidGhlbiIsImNvbnNvbGUiLCJsb2ciLCJlIiwiZXJyb3IiLCJvbiIsImVyciIsImNsb3NlIiwiZXhpdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();