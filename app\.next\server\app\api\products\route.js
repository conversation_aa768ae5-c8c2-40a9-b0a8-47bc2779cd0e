/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_OneDrive_Desktop_tindahan_app_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/Product */ \"(rsc)/./src/lib/models/Product.ts\");\n\n\n\n// GET /api/products - Get all products\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const category = searchParams.get('category');\n        const search = searchParams.get('search');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const skip = (page - 1) * limit;\n        // Build query\n        const query = {};\n        if (category && category !== 'all') {\n            query.category = category;\n        }\n        if (search) {\n            query.name = {\n                $regex: search,\n                $options: 'i'\n            };\n        }\n        // Get products with pagination\n        const products = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find(query).sort({\n            createdAt: -1\n        }).skip(skip).limit(limit);\n        // Get total count for pagination\n        const total = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: products,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching products:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch products'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/products - Create a new product\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const body = await request.json();\n        const { name, image, netWeight, price, stockQuantity, category } = body;\n        // Basic validation\n        if (!name || !netWeight || !price || !category) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        if (price < 0 || stockQuantity < 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Price and stock quantity must be non-negative'\n            }, {\n                status: 400\n            });\n        }\n        const product = new _lib_models_Product__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            name,\n            image: image || '',\n            netWeight,\n            price: parseFloat(price),\n            stockQuantity: parseInt(stockQuantity) || 0,\n            category\n        });\n        await product.save();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: product,\n            message: 'Product created successfully'\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to create product'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/Product.ts":
/*!***********************************!*\
  !*** ./src/lib/models/Product.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Product name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Product name cannot exceed 100 characters'\n        ]\n    },\n    image: {\n        type: String,\n        trim: true,\n        default: ''\n    },\n    netWeight: {\n        type: String,\n        required: [\n            true,\n            'Net weight is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Net weight cannot exceed 50 characters'\n        ]\n    },\n    price: {\n        type: Number,\n        required: [\n            true,\n            'Price is required'\n        ],\n        min: [\n            0,\n            'Price cannot be negative'\n        ],\n        validate: {\n            validator: function(value) {\n                return value >= 0;\n            },\n            message: 'Price must be a positive number'\n        }\n    },\n    stockQuantity: {\n        type: Number,\n        required: [\n            true,\n            'Stock quantity is required'\n        ],\n        min: [\n            0,\n            'Stock quantity cannot be negative'\n        ],\n        default: 0\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: {\n            values: [\n                'snacks',\n                'canned goods',\n                'beverages',\n                'personal care',\n                'household',\n                'condiments',\n                'instant foods',\n                'dairy',\n                'frozen',\n                'others'\n            ],\n            message: 'Invalid category'\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nProductSchema.index({\n    name: 1\n});\nProductSchema.index({\n    category: 1\n});\nProductSchema.index({\n    stockQuantity: 1\n});\n// Virtual for low stock indicator\nProductSchema.virtual('isLowStock').get(function() {\n    return this.stockQuantity <= 5;\n});\n// Pre-save middleware to ensure data consistency\nProductSchema.pre('save', function(next) {\n    if (this.price < 0) {\n        this.price = 0;\n    }\n    if (this.stockQuantity < 0) {\n        this.stockQuantity = 0;\n    }\n    next();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Product', ProductSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/Product.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sari-sari-store';\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        // Check if connection is still alive\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1) {\n            return cached.conn;\n        } else {\n            // Reset cached connection if it's not alive\n            cached.conn = null;\n            cached.promise = null;\n        }\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log('✅ Connected to MongoDB');\n            return mongoose.connection;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', e);\n        throw new Error('Failed to connect to database');\n    }\n    return cached.conn;\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n});\n// Handle process termination\nprocess.on('SIGINT', async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n    console.log('🛑 MongoDB connection closed due to app termination');\n    process.exit(0);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5COneDrive%5CDesktop%5Ctindahan%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();