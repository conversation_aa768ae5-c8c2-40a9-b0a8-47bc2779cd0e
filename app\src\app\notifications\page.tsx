'use client';

import { useState, useEffect } from 'react';
import Navigation from '@/components/Navigation';
import {
  Bell,
  AlertTriangle,
  Package,
  Users,
  Calendar,
  RefreshCw,
  Filter,
  ExternalLink,
  Loader2
} from 'lucide-react';
import Link from 'next/link';

interface Notification {
  id: string;
  type: 'low_stock' | 'out_of_stock' | 'overdue_debt' | 'high_risk_customer' | 'payment_reminder';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  data: any;
  createdAt: string;
}

interface NotificationData {
  notifications: Notification[];
  summary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    byType: Record<string, number>;
  };
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<NotificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/notifications');
      const data = await response.json();
      
      if (data.success) {
        setNotifications(data.data);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'low_stock':
      case 'out_of_stock':
        return <Package className="h-6 w-6" />;
      case 'overdue_debt':
      case 'payment_reminder':
        return <Calendar className="h-6 w-6" />;
      case 'high_risk_customer':
        return <Users className="h-6 w-6" />;
      default:
        return <Bell className="h-6 w-6" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityBadge = (severity: string) => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      high: 'bg-orange-100 text-orange-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-blue-100 text-blue-800'
    };
    return colors[severity as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getActionLink = (notification: Notification) => {
    switch (notification.type) {
      case 'low_stock':
      case 'out_of_stock':
        return '/products';
      case 'overdue_debt':
      case 'payment_reminder':
      case 'high_risk_customer':
        return '/debts';
      default:
        return null;
    }
  };

  const filteredNotifications = notifications?.notifications.filter(notification => {
    const severityMatch = filterSeverity === 'all' || notification.severity === filterSeverity;
    const typeMatch = filterType === 'all' || notification.type === filterType;
    return severityMatch && typeMatch;
  }) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center py-20">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="text-lg text-gray-600">Loading notifications...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Notifications & Alerts</h1>
            <p className="text-gray-600">System alerts and business notifications</p>
          </div>
          
          <button
            onClick={fetchNotifications}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mt-4 sm:mt-0"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
        </div>

        {notifications && (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
              <div className="bg-white rounded-lg p-4 shadow-sm border">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{notifications.summary.total}</p>
                  <p className="text-sm text-gray-600">Total</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border">
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{notifications.summary.critical}</p>
                  <p className="text-sm text-gray-600">Critical</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border">
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{notifications.summary.high}</p>
                  <p className="text-sm text-gray-600">High</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border">
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">{notifications.summary.medium}</p>
                  <p className="text-sm text-gray-600">Medium</p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 shadow-sm border">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{notifications.summary.low}</p>
                  <p className="text-sm text-gray-600">Low</p>
                </div>
              </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg p-4 shadow-sm border mb-6">
              <div className="flex items-center space-x-4">
                <Filter className="h-5 w-5 text-gray-600" />
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">Severity:</label>
                  <select
                    value={filterSeverity}
                    onChange={(e) => setFilterSeverity(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All</option>
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">Type:</label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All</option>
                    <option value="low_stock">Low Stock</option>
                    <option value="out_of_stock">Out of Stock</option>
                    <option value="overdue_debt">Overdue Debt</option>
                    <option value="high_risk_customer">High Risk Customer</option>
                    <option value="payment_reminder">Payment Reminder</option>
                  </select>
                </div>
                <div className="text-sm text-gray-600">
                  Showing {filteredNotifications.length} of {notifications.summary.total} notifications
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="space-y-4">
              {filteredNotifications.length > 0 ? (
                filteredNotifications.map((notification) => {
                  const actionLink = getActionLink(notification);
                  
                  return (
                    <div key={notification.id} className={`bg-white rounded-lg p-6 shadow-sm border-l-4 ${getSeverityColor(notification.severity)}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{notification.title}</h3>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityBadge(notification.severity)}`}>
                                {notification.severity.toUpperCase()}
                              </span>
                            </div>
                            <p className="text-gray-700 mb-3">{notification.message}</p>
                            
                            {/* Additional Details */}
                            {notification.data && (
                              <div className="bg-gray-50 rounded-lg p-3 text-sm">
                                {notification.type === 'low_stock' && (
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>Category: <span className="font-medium">{notification.data.category}</span></div>
                                    <div>Price: <span className="font-medium">₱{notification.data.price.toFixed(2)}</span></div>
                                  </div>
                                )}
                                {notification.type === 'overdue_debt' && (
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>Debt Count: <span className="font-medium">{notification.data.debtCount}</span></div>
                                    <div>Days Overdue: <span className="font-medium">{notification.data.daysOverdue}</span></div>
                                  </div>
                                )}
                                {notification.type === 'high_risk_customer' && (
                                  <div>
                                    <div className="mb-1">Payment Rate: <span className="font-medium">{notification.data.paymentRate}%</span></div>
                                    <div>Risk Factors: <span className="font-medium">{notification.data.riskFactors.join(', ')}</span></div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {actionLink && (
                          <Link
                            href={actionLink}
                            className="flex items-center space-x-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                          >
                            <span>Take Action</span>
                            <ExternalLink className="h-4 w-4" />
                          </Link>
                        )}
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="bg-white rounded-lg p-8 shadow-sm border text-center">
                  <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                  <p className="text-gray-600">No notifications match your current filters.</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
